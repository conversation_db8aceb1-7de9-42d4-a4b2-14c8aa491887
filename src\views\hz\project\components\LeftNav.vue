<template>
  <div class="my-form-extra-panel left-nav" :class="{ 'my-form-extra-panel-unfold': !isAdvanced }">
    <a-tooltip v-if="false" placement="right" :title="isAdvanced ? t('component.form.fold') : t('component.form.unfold')" :key="tooltipKey">
      <div class="trigger-btn right" @click="toggleAdvanced">
        <DoubleLeftOutlined v-if="isAdvanced" />
        <DoubleRightOutlined v-else />
      </div>
    </a-tooltip>
    <div class="my-form-extra-panel-main">
      <a-menu
        v-model:openKeys="openKeys"
        v-model:selectedKeys="selectedKeys"
        mode="vertical"
        :items="menuItems"
        @click="handleClick"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { reactive, toRefs, onMounted, h, ref } from 'vue';
  import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons-vue';
  import { useI18n } from '@/hooks/web/useI18n';

  import {
    FundOutlined,
    SolutionOutlined,
    CarryOutOutlined,
    AuditOutlined,
    BookOutlined,
    DollarCircleOutlined,
    BackwardOutlined,
  } from '@ant-design/icons-vue';

  const emit = defineEmits(['menuClick']);

  const state = reactive({
    isAdvanced: true,
    activeKey: '',
    tooltipKey: +new Date(),
    selectedKeys: [''],
    openKeys: [''],
    menuItems: [
      {
        key: 'exit',
        icon: () => h(BackwardOutlined),
        label: '返回',
        modelId: '',
        menuId: '',
      },
      {
        key: 'overview',
        icon: () => h(FundOutlined),
        label: '项目概览',
        modelId: '',
        menuId: '',
      },
      {
        key: 'task',
        icon: () => h(SolutionOutlined),
        label: '任务管理',
        modelId: '',
        menuId: '',
      },
      {
        key: 'progress',
        icon: () => h(CarryOutOutlined),
        label: '进度管理',
        modelId:'',
        // modelId: '707984386888305541',
        menuId: '707986542374685573',
      },
      {
        key: 'quality',
        icon: () => h(AuditOutlined),
        label: '质量管理',
        modelId: '708036171288021893',
        menuId: '705771608328899205',
        enableFlow: true,
      },
      {
        key: 'archive',
        icon: () => h(BookOutlined),
        label: '档案管理',
        modelId: '705773175815801477',
        menuId: '725280950522483525',
      },
      {
        key: 'allocation',
        icon: () => h(DollarCircleOutlined),
        label: '效益管理',
        modelId: '',
        // modelId: '708293041642802053',
        menuId: '705776502767094405',
      },
    ],
  });

  const { isAdvanced, tooltipKey, openKeys, selectedKeys, menuItems } = toRefs(state);
  const { t } = useI18n();

  const handleClick = menuInfo => {
    state.activeKey = menuInfo.key;
    emit('menuClick', menuInfo.item.originItemValue);
  };

  function toggleAdvanced() {
    state.isAdvanced = !state.isAdvanced;
    state.tooltipKey = +new Date();
  }

  onMounted(() => {
    state.activeKey = 'overview';
    state.selectedKeys = ['overview'];
    state.openKeys = ['overview'];
  });
</script>
<style scoped>
.my-form-extra-panel-main {
  height: 100%;
  overflow: scroll;
}
.ant-menu {
  height: 100%;
  width: 200px;
}
</style>
