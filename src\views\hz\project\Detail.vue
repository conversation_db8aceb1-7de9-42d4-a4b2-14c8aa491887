<template>
  <BasicPopup
    v-bind="$attrs"
    @register="registerPopup"
    :showHeader="false"
    destroyOnClose
    :closeFunc="onClose"
    class="full-popup hz-project-detail pz-style"
    :showCancelBtn="false"
    cancelText="返回项目列表">
    <template #title> </template>
    <template #insertToolbar>
      <a-button
        class="ml-10px"
        v-for="item in state.customBtns"
        :key="item.value"
        type="primary"
        :preIcon="item.actionConfig?.btnIcon"
        v-if="!loading"
        @click="customBtnsHandle(item)">
        {{ item.label ? item.label : t(item.labelI18nCode, item.label) }}
      </a-button>
      <a-button class="ml-10px" type="primary" @click="handlePrint" v-if="formConf.hasPrintBtn && formConf.printId">{{ getPrintText }}</a-button>
    </template>
    <div class="jnpf-common-form-wrapper">
      <!-- 左侧菜单 -->
      <LeftNav @menuClick="handleMenuClick" />
      <div class="jnpf-common-form-wrapper__main right-container" :style="{ margin: '0 auto', width: formConf.fullScreenWidth || '100%' }">
        <div class="project-info-pane">
          <span class="project-title">{{ title }}</span>
          <PzTag :color="state.config.projectStyle.f_color">{{ state.config.projectStyle.f_name }}</PzTag>
          <PzTag :color="getProjectStatusColor(state.config.record.f_project_status_jnpfId)">{{ state.config.record.f_project_status }}</PzTag>
          <span class="project-code">#{{ state.config.record.f_project_code }}</span>
        </div>
        <!-- 页签 -->
        <template v-if="!leftMenuInfo.key || leftMenuInfo.key === 'overview'">
          <template v-if="!loading && !hideExtra">
            <a-tabs v-model:activeKey="extraActiveKey" class="jnpf-content-wrapper-tabs" destroyInactiveTabPane @change="onTabChange">
              <a-tab-pane v-for="(item, index) in extraList" :key="index" :tab="item.fullName"></a-tab-pane>
            </a-tabs>
            <div class="jnpf-content-detail-extra">
              <template v-if="extraList[extraActiveKey].id == 'info'">
                <template v-if="!state.isEditing">
                  <div class="parser-btns">
                    <a-button class="handleBtn" type="primary" @click="handleEdit">编辑</a-button>
                  </div>
                  <Parser
                    class="!pt-0px hz-project-tab-info"
                    ref="parserRef"
                    :formConf="formConf"
                    :formData="formData"
                    @toDetail="toDetail"
                    :key="key + 'info'" />
                </template>
                <template v-else>
                  <Form :key="key + 'form'" ref="editFormRef" @close="onFormClose" @reload="onFormClose" />
                </template>
              </template>
              <template v-else-if="extraList[extraActiveKey].id == 'instruct'">
                <div class="parser-btns">
                  <a-button class="handleBtn" type="primary" @click="handleInstruct">下发指令</a-button>
                </div>
                <Parser class="!pt-0px hz-project-tab-instruct" ref="parserRef" :formConf="formConf" :formData="formData" @toDetail="toDetail" />
                <FlowParserModal title="下发指令" @register="registerFlowParserModal" @reload="onInstructClose" @close="onInstructClose" />
              </template>
              <template v-else-if="extraList[extraActiveKey].id == 'contract'">
                <div class="parser-btns">
                  <a-button class="handleBtn" type="primary" @click="handleContract">合同报审</a-button>
                </div>
                <Parser class="!pt-0px hz-project-tab-contract" ref="parserRef" :formConf="formConf" :formData="formData" @toDetail="toDetail" />
                <FlowParserModal title="合同报审" @register="registerFlowParserModal" @reload="onContractClose" @close="onContractClose" />
              </template>
              <template v-else-if="extraList[extraActiveKey].id == 'overview'">
                <DynamicPortal portalId="710518089782922373"></DynamicPortal>
              </template>
              <div v-else class="h-full" v-loading="extraLoading">
                <List
                  class="sublist"
                  ref="extraListRef"
                  :config="extraList[extraActiveKey]?.extraConfig"
                  :modelId="extraList[extraActiveKey]?.extraConfig.id"
                  :key="extraKey"
                  v-if="extraList[extraActiveKey]?.extraConfig && !extraLoading" />
                <div v-else>loading</div>
                <jnpf-empty class="extra-empty" v-if="!extraList[extraActiveKey]?.extraConfig && !extraLoading" />
              </div>
            </div>
          </template>
          <!-- 修改记录 -->
          <FormExtraPanel
            v-bind="getFormExtraBind"
            v-if="state.dataForm.id && formConf.dataLog && ['info', 'contract', 'instruct'].includes(extraList[extraActiveKey].id)" />
        </template>
        <!--  子列表  -->
        <template v-if="leftMenuKey && leftMenuConfig[leftMenuKey]">
          <List class="sublist" :key="leftMenuKey" :config="leftMenuConfig[leftMenuKey]" :modelId="leftMenuKey" :menuId="leftMenuId" />
        </template>
        <template v-else-if="leftMenuInfo.key === 'task'">
          <TaskIndex />
        </template>
        <template v-else-if="leftMenuInfo.key === 'progress'">
          <ProgressIndex />
        </template>
        <template v-else-if="leftMenuInfo.key === 'allocation'">
          <benefitIndex />
        </template>

      </div>
    </div>
  </BasicPopup>
  <Detail v-if="detailVisible" ref="detailRef" @close="state.detailVisible = false" />
  <Form v-if="formVisible" ref="formRef" @reload="reloadTable" />
  <PrintSelect @register="registerPrintSelect" @change="handleShowBrowse" />
  <PrintBrowse @register="registerPrintBrowse" />
  <CustomForm ref="customFormRef" />
</template>
<script lang="ts" setup>
  import { getDataChange, getConfigData, getConfigDataByMenuId, getModelInfo, launchFlow } from '@/api/onlineDev/visualDev';
  import { getDataInterfaceRes } from '@/api/systemData/dataInterface';
  import { reactive, toRefs, nextTick, ref, computed, onUnmounted } from 'vue';
  import { BasicPopup, usePopup } from '@/components/Popup';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import { useGeneratorStore } from '@/store/modules/generator';
  import { cloneDeep } from 'lodash-es';
  import { getScriptFunc, onlineUtils, getParamList, getLaunchFlowParamList } from '@/utils/jnpf';
  import { getProjectStatusColor } from '../util';

  import Parser from '@/views/common/dynamicModel/list/detail/Parser.vue';
  import Form from '@/views/common/dynamicModel/list/Form.vue';
  import PrintSelect from '@/components/PrintDesign/printSelect/index.vue';
  import PrintBrowse from '@/components/PrintDesign/printBrowse/index.vue';
  import CustomForm from '@/views/common/dynamicModel/list/CustomForm.vue';
  import FormExtraPanel from '@/components/FormExtraPanel/index.vue';
  import LeftNav from './components/LeftNav.vue';
  import List from '@/views/common/dynamicModel/list/index.vue';
  import DynamicPortal from './protal/index.vue';
  import FlowParserModal from '@/views/workFlow/components/FlowParserModal.vue';
  import TaskIndex from './task/index.vue';
  // import FlowList from './components/FlowList.vue';
  import ProgressIndex from './progress/index.vue';
  import benefitIndex from './benefit/index.vue';
  import PzTag from '@/components/Pz/PzTag/index.vue';

  interface State {
    formConf: any;
    formData: any;
    config: any;
    loading: boolean;
    key: number;
    dataForm: any;
    formOperates: any[];
    title: string;
    detailVisible: boolean;
    formVisible: boolean;
    customBtns: any[];
    extraList: any[];
    extraActiveKey: number;
    extraConfig: any;
    extraLoading: boolean;
    extraKey: number;
    hideExtra: boolean;
    leftMenuKey: string;
    leftMenuId: string;
    leftMenuInfo: any;
    leftMenuConfig: any;
    isEditing: boolean;
    isInstructing: boolean;
    isContracting: boolean;
    allList: any[];
  }

  defineOptions({ name: 'HzProjectDetail' });
  defineExpose({ init });

  const emit = defineEmits(['close']);
  const { t } = useI18n();
  const { createMessage, createConfirm } = useMessage();
  const [registerFlowParserModal, { openModal: openFlowParserModal }] = useModal();
  const [registerPopup, { openPopup, closePopup, setPopupProps }] = usePopup();
  const [registerPrintSelect, { openModal: openPrintSelect }] = useModal();
  const [registerPrintBrowse, { openModal: openPrintBrowse }] = useModal();
  const userStore = useUserStore();
  const generatorStore = useGeneratorStore();
  const parserRef = ref<any>(null);
  const detailRef = ref<any>(null);
  const customFormRef = ref<any>(null);
  const formRef = ref<any>(null);
  const editFormRef = ref<any>(null);
  const extraListRef = ref<any>(null);
  const state = reactive<State>({
    formConf: {},
    formData: {},
    config: {},
    loading: false,
    key: +new Date(),
    dataForm: {
      id: '',
      data: '',
    },
    formOperates: [],
    title: t('common.detailText'),
    detailVisible: false,
    formVisible: false,
    customBtns: [],
    extraList: [],
    extraActiveKey: 0,
    extraConfig: {},
    extraLoading: false,
    extraKey: 0,
    hideExtra: false,
    leftMenuKey: '',
    leftMenuId: '',
    leftMenuInfo: {},
    leftMenuConfig: {},
    isEditing: false,
    isInstructing: false,
    isContracting: false,
    allList: [],
  });
  const {
    title,
    formConf,
    formData,
    key,
    loading,
    detailVisible,
    formVisible,
    extraList,
    extraActiveKey,
    extraLoading,
    extraKey,
    hideExtra,
    leftMenuInfo,
    leftMenuConfig,
    leftMenuKey,
    leftMenuId,
  } = toRefs(state);

  const getPrintText = computed(() => {
    const text = state.formConf.printButtonTextI18nCode
      ? t(state.formConf.printButtonTextI18nCode, state.formConf.printButtonText)
      : state.formConf.printButtonText;
    return text || t('common.printText');
  });
  const getFormExtraBind = computed(() => ({ showLog: state.formConf.dataLog, modelId: state.config.modelId, formDataId: state.config.id }));

  function fillFormData(form, data) {
    const loop = (list, parent?) => {
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        if (item.__vModel__) {
          if (item.__config__.jnpfKey === 'relationForm' || item.__config__.jnpfKey === 'popupSelect') {
            item.__config__.defaultValue = data[item.__vModel__ + '_id'];
            item.name = data[item.__vModel__] || '';
          } else {
            const val = data.hasOwnProperty(item.__vModel__) ? data[item.__vModel__] : item.__config__.defaultValue;
            item.__config__.defaultValue = val;
          }
          if (!state.config.isDataManage && state.config.useFormPermission) {
            let id = item.__config__.isSubTable ? parent.__vModel__ + '-' + item.__vModel__ : item.__vModel__;
            let noShow = true;
            if (state.formOperates && state.formOperates.length) {
              noShow = !state.formOperates.some(o => o.enCode === id);
            }
            noShow = item.__config__.noShow ? item.__config__.noShow : noShow;
            item.__config__.noShow = noShow;
          }
        } else {
          if (['relationFormAttr', 'popupAttr'].includes(item.__config__.jnpfKey)) {
            item.__config__.defaultValue = data[item.relationField.split('_jnpfTable_')[0] + '_' + item.showField];
          }
        }
        if (item.__config__ && item.__config__.children && Array.isArray(item.__config__.children)) {
          loop(item.__config__.children, item);
        }
      }
    };
    loop(form.fields);
  }
  function init(data) {
    state.loading = true;
    state.config = data;
    state.formConf = cloneDeep(data.formConf);
    state.customBtns = (state.formConf.customBtns || []).reverse();
    state.dataForm.id = data.id;
    state.extraActiveKey = 0;
    state.extraConfig = {};
    state.hideExtra = data.hideExtra || false;
    state.allList = data.allList;
    state.title = state.config.record?.f_project_name || t('common.detailText');
    state.leftMenuInfo = {};
    state.leftMenuConfig = {};
    state.leftMenuKey = '';
    state.leftMenuId = '';
    getFormOperates();
    getExtraList();
    openForm();

    // 将当前项目信息写入本地存储，让低代码表单可以获取
    localStorage.setItem('project_id', data.record.f_id);
    localStorage.setItem('project_code', data.record.f_project_code);
    localStorage.setItem('project_type', data.record.f_project_type_id);
    localStorage.setItem('project_type_id', data.record.f_project_type_id_jnpfId);
    localStorage.setItem('project_manager_id', data.record.f_project_manager_id_jnpfId);
    localStorage.setItem('execution_manager_id', data.record.f_execution_manager_id_jnpfId);
    localStorage.setItem('project_allocation_amount', data.record.f_contract_amount);

    document.querySelector('#app')?.setAttribute('isProjectDetail', 'true');
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    state.loading = true;
    if (state.config.id) {
      const extra = { modelId: state.config.modelId, id: state.config.id, type: 2 };
      generatorStore.setDynamicModelExtra(extra);
      getInfo(state.config.id, state.config.propsValue);
    } else {
      closeForm();
    }
  }
  function getInfo(id, propsValue) {
    let query: any = {
      id: id,
    };
    if (propsValue) query = { ...query, propsValue };
    getDataChange(state.config.modelId, query).then(res => {
      state.dataForm = res.data || {};
      if (!state.dataForm.data) return;
      state.formData = JSON.parse(state.dataForm.data);

      fillFormData(state.formConf, state.formData);
      initRelationForm(state.formConf.fields);
      nextTick(() => {
        state.loading = false;
        state.key = +new Date();
        changeLoading(false);
      });
    });
  }
  function initRelationForm(componentList) {
    componentList.forEach(cur => {
      const config = cur.__config__;
      if (config.jnpfKey == 'relationFormAttr' || config.jnpfKey == 'popupAttr') {
        const relationKey = cur.relationField.split('_jnpfTable_')[0];
        componentList.forEach(item => {
          const noVisibility = Array.isArray(item.__config__.visibility) && !item.__config__.visibility.includes('pc');
          if (relationKey == item.__vModel__ && (noVisibility || !!item.__config__.noShow) && !cur.__vModel__) {
            cur.__config__.noShow = true;
          }
        });
      }
      if (cur.__config__.children && cur.__config__.children.length) initRelationForm(cur.__config__.children);
    });
  }
  function getFormOperates() {
    if (state.config.isPreview || state.config.isDataManage || !state.config.useFormPermission) return;
    const permissionList = userStore.getPermissionList;
    const modelId = state.config.menuId;
    const list = permissionList.filter(o => o.modelId === modelId);
    state.formOperates = list[0] && list[0].form ? list[0].form : [];
  }
  function toDetail(item) {
    if (!item.__config__.defaultValue) return;
    getConfigData(item.modelId).then(res => {
      if (!res.data) return;
      if (!res.data.formData) return;
      const formConf = JSON.parse(res.data.formData);
      formConf.popupType = state.formData.popupType;
      formConf.customBtns = [];
      formConf.hasPrintBtn = false;
      const data = {
        id: item.__config__.defaultValue,
        formConf,
        modelId: item.modelId,
        propsValue: item.propsValue,
      };
      state.detailVisible = true;
      nextTick(() => {
        detailRef.value?.init(data);
      });
    });
  }
  function handleEdit() {
    state.isEditing = true;
    const data = {
      id: state.config.id,
      formConf: state.formConf,
      modelId: state.config.modelId,
      isPreview: state.config.isPreview,
      isDataManage: state.config.isDataManage,
      useFormPermission: state.config.useFormPermission,
      showMoreBtn: false,
      menuId: state.config.menuId,
      allList: [],
    };
    nextTick(() => {
      editFormRef.value?.init(data);
    });
  }
  function onFormClose() {
    state.isEditing = false;
  }
  function handleInstruct() {
    state.isInstructing = true;
    const data = {
      id: '',
      flowId: '705110970464209477',
      opType: '-1',
      isFlow: 1,
    };
    nextTick(() => {
      openFlowParserModal(true, data);
    });
  }
  function onInstructClose() {
    state.isInstructing = false;
  }
  function handleContract() {
    state.isContracting = true;
    const data = {
      id: '',
      flowId: '707992923781729157',
      opType: '-1',
      isFlow: 1,
    };
    nextTick(() => {
      openFlowParserModal(true, data);
    });
  }
  function onContractClose() {
    state.isContracting = false;
  }
  function handlePrint() {
    if (state.config.isPreview) return createMessage.warning('功能预览不支持打印');
    if (!state.formConf.printId?.length) return createMessage.error('未配置打印模板');
    if (state.formConf.printId?.length === 1) return handleShowBrowse(state.formConf.printId[0]);
    openPrintSelect(true, state.formConf.printId);
  }
  function handleShowBrowse(id) {
    openPrintBrowse(true, { id, formInfo: [{ formId: state.dataForm.id }] });
  }
  function openForm() {
    if (state.formConf.popupType === 'fullScreen') return openPopup();
  }
  function closeForm() {
    if (state.formConf.popupType === 'fullScreen') return closePopup();
  }
  function setFormProps(data) {
    if (state.formConf.popupType === 'fullScreen') return setPopupProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }
  async function onClose() {
    emit('close');
    // console.log('close');
    document.querySelector('#app')?.removeAttribute('isProjectDetail');
    return true;
  }
  // 自定义按钮点击事件
  function customBtnsHandle(item) {
    if (item.actionConfig.btnType == 1) handlePopup(item.actionConfig);
    if (item.actionConfig.btnType == 2) handleScriptFunc(item.actionConfig);
    if (item.actionConfig.btnType == 3) handleInterface(item.actionConfig);
    if (item.actionConfig.btnType == 4) handleLaunchFlow(item);
  }
  function handlePopup(item) {
    const data = {
      ...item,
      recordModelId: state.config.modelId,
      record: state.formData,
    };
    customFormRef.value?.init(data);
  }
  function handleScriptFunc(item) {
    const parameter = { data: state.formData, onlineUtils };
    const func: any = getScriptFunc(item.func);
    if (!func) return;
    func(parameter);
  }
  function handleInterface(item) {
    const handlerData = () => {
      getModelInfo(state.config.modelId, state.config.id).then(res => {
        const dataForm = res.data || {};
        if (!dataForm.data) return;
        const data = { ...JSON.parse(dataForm.data), id: state.config.id };
        handlerInterface(data);
      });
    };
    const handlerInterface = data => {
      const query = { paramList: getParamList(item.templateJson, { ...data, id: state.config.id }) || [] };
      getDataInterfaceRes(item.interfaceId, query).then(res => {
        createMessage.success(res.msg);
      });
    };
    if (!item.useConfirm) return handlerData();
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: item.confirmTitle || '确认执行此操作?',
      onOk: () => {
        handlerData();
      },
    });
  }
  function handleLaunchFlow(item) {
    const launchFlowCfg = cloneDeep(item.actionConfig.launchFlow);
    const query = {
      template: launchFlowCfg.flowId,
      btnCode: item.value,
      currentUser: launchFlowCfg.currentUser,
      customUser: launchFlowCfg.customUser,
      initiator: launchFlowCfg.initiator,
      dataList: [getLaunchFlowParamList(launchFlowCfg.transferList, state.formData)],
    };
    launchFlow(state.config.modelId, query).then(res => {
      createMessage.success(res.msg);
    });
  }
  function getExtraList() {
    state.extraList = [
      { fullName: '项目概览', id: 'overview' },
      { fullName: '项目信息', id: 'info' },
      { fullName: '指令下发', id: 'instruct' },
      { fullName: '合同信息', id: 'contract' },
      ...state.formConf.detailExtraList,
    ];
  }
  function onTabChange(index) {
    state.isEditing = state.isInstructing = state.isContracting = false;
    if (state.extraList[index]?.extraConfig) return (state.extraKey = +new Date());
    state.extraLoading = true;
    getConfig(state.extraList[index]?.targetFormId, index);
  }
  function getConfig(menuId, index) {
    if (!menuId) return (state.extraLoading = false);
    getConfigDataByMenuId({ menuId, systemId: userStore.getUserInfo?.systemId })
      .then(res => {
        if (res.code !== 200 || !res.data) {
          state.extraList[index].extraConfig = '';
          state.extraLoading = false;
          state.extraKey = +new Date();
          return;
        }
        state.extraList[index].extraConfig = res.data;
        state.extraList[index].extraConfig.extraQueryJson = JSON.stringify({ f_project_code: state.formData.f_project_code });
        state.extraLoading = false;
        state.extraKey = +new Date();
      })
      .catch(() => {
        state.extraLoading = false;
        state.extraKey = +new Date();
      });
  }
  function handleOpenDetail(data) {
    state.detailVisible = true;
    nextTick(() => {
      detailRef.value?.init(data);
    });
  }
  function handleOpenForm(data) {
    state.formVisible = true;
    nextTick(() => {
      formRef.value?.init(data);
    });
  }
  function reloadTable() {
    extraListRef.value?.reload();
  }
  function handleMenuClick(menuInfo) {
    state.leftMenuInfo = menuInfo;
    state.leftMenuKey = menuInfo.modelId;
    state.leftMenuId = menuInfo.menuId;
    if (menuInfo.key === 'exit') {
      closePopup();
      onClose();
      return;
    }
    if (menuInfo.modelId && !state.leftMenuConfig[menuInfo.modelId]) {
      getConfigData(menuInfo.modelId).then(res => {
        state.leftMenuConfig[menuInfo.modelId] = res.data;
        state.leftMenuConfig[menuInfo.modelId].id = state.leftMenuConfig[menuInfo.modelId].id || menuInfo.modelId;
        state.leftMenuConfig[menuInfo.modelId].extraQueryJson = JSON.stringify({ f_project_code: state.formData.f_project_code });
        state.leftMenuConfig[menuInfo.modelId].enableFlow = menuInfo.enableFlow;
      });
    }
    // console.log('click ', menuInfo);
  }
  onUnmounted(() => {
    // console.log('unmounted', state.config.id);
    if (state.config.id != undefined) {
      // 避免在其他页面触发
      document.querySelector('#app')?.removeAttribute('isProjectDetail');
    }
  });
</script>
<style>
  .title-tag,
  .jnpf-text-tag {
    margin-left: 8px;
    margin-top: 3px;
  }
  .jnpf-content-detail-extra {
    overflow: initial !important;
    padding-top: 0;
    .hz-project-tab-info,
    .hz-project-tab-contract,
    .hz-project-tab-instruct {
      margin: 0 10px;
    }
    .hz-project-customer {
      display: none;
    }
  }

  .jnpf-basic-caption {
    padding: 10px;
  }
  .dynamic-form,
  .hz-project-tab-info {
    .hz-project-contract,
    .hz-project-instruct {
      display: none;
    }
  }
  .hz-project-tab-contract {
    .hz-project-info,
    .hz-project-instruct {
      display: none;
    }
  }
  .hz-project-tab-instruct {
    .hz-project-info,
    .hz-project-contract {
      display: none;
    }
  }
  .parser-btns {
    text-align: right;
  }
  .hz-project-detail {
    .handleBtn {
      margin: 10px 10px 10px 0;
    }
    .right-container {
      /* height: fit-content; */
    }
  }

  #app[isProjectDetail='true'] {
    section.ant-layout-has-sider > div,
    aside.ant-layout-sider {
      width: 0 !important;
      min-width: 0 !important;
      flex: 0 0 0 !important;
      margin-left: 0 !important;
    }
    section.jnpf-default-layout-main > div {
      height: 50px !important;
    }
  }
  .sublist .jnpf-basic-caption {
    padding: 10px;
  }
  .sublist .jnpf-content-wrapper-search-box {
    margin-bottom: 0 !important;
    margin-top: 10px;
  }
  .project-code {
    opacity: 0.7;
    margin: 0 5px;
  }
  .jnpf-basic-title {
    font-weight: bold !important;
  }
  .project-info-pane {
    display: flex;
    gap: 10px;
    align-items: baseline;
    padding: 10px;
    .project-title {
      font-size: 26px;
      font-weight: bold;
    }
  }
  .ant-table-wrapper .ant-table.ant-table-small {
    .ant-table-tbody > tr > td,
    .ant-table-thead > tr > th {
      padding: 16px;
    }
  }
</style>
