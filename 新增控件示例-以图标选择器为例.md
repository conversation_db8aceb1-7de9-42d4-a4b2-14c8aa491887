# 新增控件示例-以图标选择器为例

## 概述

本文档详细说明了如何在JNPF表单生成器中新增一个自定义控件。

以图标选择器(IconPicker)为例，展示了从控件定义到列表渲染支持的完整开发流程。

## 实现步骤

### 第一步：在组件映射中定义控件

**文件:** `src/components/FormGenerator/src/helper/componentMap.ts`

在`inputComponents`数组中添加新控件的配置：

```typescript
{
  __config__: {
    jnpfKey: 'iconPicker',           // 控件唯一标识
    label: '图标选择',                // 控件显示名称
    tipLabel: '',                    // 提示标签
    labelWidth: undefined,           // 标签宽度
    showLabel: true,                 // 是否显示标签
    tag: 'JnpfIconPicker',          // 对应的Vue组件名
    tagIcon: 'ym-custom ym-custom-flag-outline', // 控件图标
    tableAlign: 'left',             // 表格对齐方式
    tableFixed: 'none',             // 表格固定方式
    className: [],                  // CSS类名
    defaultValue: null,             // 默认值
    required: false,                // 是否必填
    layout: 'colFormItem',          // 布局类型
    span: 24,                       // 栅格占位
    dragDisabled: false,            // 是否禁用拖拽
    visibility: ['pc', 'app'],      // 可见性设置
    tableName: '',                  // 表名
    noShow: false,                  // 是否隐藏
    regList: [],                    // 验证规则列表
    trigger: 'change',              // 触发事件
  },
  disabled: false,                  // 是否禁用
}
```

### 第二步：配置控件使用限制

**文件:** `src/components/FormGenerator/src/helper/config.ts`

根据你的控件特性，添加控件到相应的限制列表中：

```typescript
// 不可以添加到搜索
const noSearchList = [
  ...noColumnShowList,
  // ... 其他配置
  'iconPicker',
  // ... 其他配置
];

// 不允许关联到联动里面的控件
const noAllowRelationList = ['table', 'uploadImg', 'uploadFile', 'modifyUser', 'modifyTime', 'dataLogList', 'iconPicker'];

// 不允许分组和排序
const noGroupList = ['sign', 'signature', 'location', 'uploadImg', 'uploadFile', 'editor', 'dataLogList', 'iconPicker'];
```

### 第三步：配置右侧面板

**文件:** `src/components/FormGenerator/src/helper/rightPanel.ts`

根据你的控件特性，将控件添加到不设置宽度的列表中：

```typescript
//不设置宽度
export const noWithList = [...layoutList, 'switch', 'radio', 'checkbox', 'uploadFile', 'uploadImg', 'colorPicker', 'iconPicker', 'rate', 'qrcode', 'barcode', 'editor'];
```

### 第四步：创建右侧配置组件

**文件:** `src/components/FormGenerator/src/rightComponents/RIconPicker.vue`

```vue
<template>
  <a-form-item label="默认值">
    <jnpf-icon-picker
      v-model:value="activeData.__config__.defaultValue"
      :key="renderKey"
      :disabled="activeData.disabled" />
  </a-form-item>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  defineOptions({ inheritAttrs: false });
  const props = defineProps(['activeData']);
  const renderKey = ref(+new Date());
</script>
```

### 第五步：注册右侧配置组件

**文件:** `src/components/FormGenerator/src/rightComponents/index.ts`

```typescript
export { default as RIconPicker } from './RIconPicker.vue';
```

### 第六步：添加列表渲染支持

**文件:** `src/views/common/dynamicModel/list/index.vue`

在列表组件中添加对图标选择器的渲染支持：

```vue
<!-- 在子表渲染部分 -->
<template v-if="column.jnpfKey === 'iconPicker'">
  <i :class="childRecord[column.dataIndex]"></i>{{ childRecord[column.dataIndex] }}
</template>

<!-- 在主表渲染部分 -->
<template v-else-if="column.jnpfKey === 'iconPicker'">
  <i :class="record[column.prop]"></i>{{ record[column.prop] }}
</template>

<!-- 在详情渲染部分 -->
<template v-else-if="column.jnpfKey === 'iconPicker'">
  <i :class="record[column.prop + '_name']"></i>
</template>
```

## 关键要点

### 1. 控件标识符
- `jnpfKey`: 控件的唯一标识符，用于在整个系统中识别控件类型
- `tag`: 对应的Vue组件名，必须与实际组件名匹配

### 2. 配置限制
- 根据控件特性合理配置使用限制
- 考虑控件在不同场景下的适用性（子表、搜索、列表展示等）

### 3. 渲染支持
- 在列表页面添加对应的渲染模板
- 考虑不同显示场景（主表、子表、详情）的渲染需求

### 4. 右侧配置
- 为控件提供必要的配置选项
- 使用合适的表单控件来配置属性

## 扩展指南

基于此示例，开发其他控件时需要注意：

1. **确保组件存在**: 确保`tag`指定的Vue组件已经在系统中注册
2. **图标配置**: 为控件选择合适的图标标识
3. **默认值设置**: 根据控件类型设置合理的默认值
4. **验证规则**: 如需要，添加相应的验证规则配置
5. **样式适配**: 确保控件在不同场景下的样式表现正常

## 总结

通过图标选择器的实现示例，我们可以看到新增控件的完整流程：
1. 定义控件配置
2. 设置使用限制
3. 创建配置界面
4. 添加渲染支持
5. 优化配置限制

这个流程可以作为开发其他自定义控件的标准模板。
