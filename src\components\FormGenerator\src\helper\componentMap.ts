import type { GenItem } from '../types/genItem';
// 表单属性【右面板】
export const formConf = {
  formRef: 'formRef',
  formModel: 'dataForm',
  size: 'middle', //large,middle,small
  labelPosition: 'right',
  labelWidth: 100,
  labelSuffix: '', // 标题后缀
  formRules: 'rules',
  popupType: 'general',
  generalWidth: '600px',
  fullScreenWidth: '100%',
  drawerWidth: '600px',
  gutter: 15,
  disabled: false,
  span: 24,
  colon: false,
  hasCancelBtn: true,
  cancelButtonText: '取消',
  cancelButtonTextI18nCode: 'common.cancelText',
  hasConfirmBtn: true,
  confirmButtonText: '确定',
  confirmButtonTextI18nCode: 'common.okText',
  hasConfirmAndAddBtn: true,
  hasPrintBtn: false,
  printButtonText: '打印',
  printButtonTextI18nCode: 'common.printText',
  customBtns: [],
  primaryKeyPolicy: 1,
  concurrencyLock: false,
  logicalDelete: false,
  dataLog: false,
  useBusinessKey: false,
  businessKeyList: [],
  businessKeyTip: '数据已存在，请勿重复提交！',
  printId: '',
  formStyle: '',
  classNames: [],
  className: [],
  classJson: '',
  detailExtraList: [], //详情页tab信息
  funcs: {
    onLoad: '({ formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    beforeSubmit:
      '({ formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    return new Promise((resolve, reject) => {\n        // 在此编写代码\n        \n        // 继续执行\n        resolve()\n    })\n}',
    afterSubmit: '({ formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
  },
  fields: [],
};

// 基础控件 【左面板】
export const inputComponents: GenItem[] = [
  {
    __config__: {
      jnpfKey: 'input',
      label: '单行输入',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfInput',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: undefined,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'blur',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请输入',
    useScan: false,
    useMask: false,
    maskConfig: {
      filler: '*',
      maskType: 1,
      prefixType: 1,
      prefixLimit: 0,
      prefixSpecifyChar: '',
      suffixType: 1,
      suffixLimit: 0,
      suffixSpecifyChar: '',
      ignoreChar: '',
      useUnrealMask: false,
      unrealMaskLength: 1,
    },
    clearable: true,
    addonBefore: '',
    addonAfter: '',
    prefixIcon: '',
    suffixIcon: '',
    maxlength: null,
    showCount: false,
    showPassword: false,
    readonly: false,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'textarea',
      label: '多行输入',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfTextarea',
      tagIcon: 'icon-ym icon-ym-generator-textarea',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: undefined,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'blur',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请输入',
    autoSize: {
      minRows: 4,
      maxRows: 4,
    },
    clearable: true,
    maxlength: null,
    showCount: false,
    readonly: false,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'inputNumber',
      label: '数字输入',
      tipLabel: '',
      showLabel: true,
      labelWidth: undefined,
      tag: 'JnpfInputNumber',
      tagIcon: 'icon-ym icon-ym-generator-number',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: undefined,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: ['blur', 'change'],
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请输入',
    min: undefined,
    max: undefined,
    controls: false,
    addonBefore: '',
    addonAfter: '',
    thousands: false,
    isAmountChinese: false,
    step: 1,
    precision: undefined,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'switch',
      label: '开关',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfSwitch',
      tagIcon: 'icon-ym icon-ym-generator-switch',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: 0,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    disabled: false,
    activeTxt: '开',
    inactiveTxt: '关',
    activeValue: 1,
    inactiveValue: 0,
  },
  {
    __config__: {
      jnpfKey: 'radio',
      label: '单选框组',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfRadio',
      tagIcon: 'icon-ym icon-ym-generator-radio',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: undefined,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      dataType: 'static',
      dictionaryType: '',
      propsUrl: '',
      propsName: '',
      useCache: true,
      templateJson: [],
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    options: [
      {
        fullName: '选项一',
        id: '1',
      },
      {
        fullName: '选项二',
        id: '2',
      },
    ],
    props: {
      label: 'fullName',
      value: 'id',
    },
    direction: 'horizontal',
    optionType: 'default',
    buttonStyle: 'solid',
    size: 'default',
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'checkbox',
      label: '多选框组',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfCheckbox',
      tagIcon: 'icon-ym icon-ym-generator-checkbox',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: [],
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      dataType: 'static',
      dictionaryType: '',
      propsUrl: '',
      propsName: '',
      useCache: true,
      templateJson: [],
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    options: [
      {
        fullName: '选项一',
        id: '1',
      },
      {
        fullName: '选项二',
        id: '2',
      },
    ],
    props: {
      label: 'fullName',
      value: 'id',
    },
    direction: 'horizontal',
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'select',
      label: '下拉选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfSelect',
      tagIcon: 'icon-ym icon-ym-generator-select',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      dataType: 'static',
      dictionaryType: '',
      propsUrl: '',
      propsName: '',
      useCache: true,
      templateJson: [],
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    options: [
      {
        fullName: '选项一',
        id: '1',
      },
      {
        fullName: '选项二',
        id: '2',
      },
    ],
    props: {
      label: 'fullName',
      value: 'id',
    },
    placeholder: '请选择',
    clearable: true,
    disabled: false,
    filterable: false,
    multiple: false,
  },
  {
    __config__: {
      jnpfKey: 'cascader',
      label: '级联选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfCascader',
      tagIcon: 'icon-ym icon-ym-generator-cascader',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: [],
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      dataType: 'static',
      propsUrl: '',
      propsName: '',
      useCache: true,
      templateJson: [],
      dictionaryType: '',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    options: [
      {
        id: '1',
        fullName: '选项1',
        children: [
          {
            id: '2',
            fullName: '选项1-1',
          },
        ],
      },
    ],
    props: {
      value: 'id',
      label: 'fullName',
      children: 'children',
    },
    placeholder: '请选择',
    disabled: false,
    clearable: true,
    filterable: false,
    multiple: false,
  },
  {
    __config__: {
      jnpfKey: 'datePicker',
      label: '日期选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfDatePicker',
      tagIcon: 'icon-ym icon-ym-generator-date',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      startTimeRule: false,
      startTimeType: 1,
      startTimeTarget: 1,
      startTimeValue: null,
      startRelationField: '',
      endTimeRule: false,
      endTimeType: 1,
      endTimeTarget: 1,
      endTimeValue: null,
      endRelationField: '',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    format: 'yyyy-MM-dd',
    startTime: null,
    endTime: null,
    disabled: false,
    clearable: true,
  },
  {
    __config__: {
      jnpfKey: 'timePicker',
      label: '时间选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfTimePicker',
      tagIcon: 'icon-ym icon-ym-generator-time',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      startTimeRule: false,
      startTimeType: 1,
      startTimeTarget: 1,
      startTimeValue: null,
      startRelationField: '',
      endTimeRule: false,
      endTimeType: 1,
      endTimeTarget: 1,
      endTimeValue: null,
      endRelationField: '',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    format: 'HH:mm:ss',
    startTime: null,
    endTime: null,
    disabled: false,
    clearable: true,
  },
  {
    __config__: {
      jnpfKey: 'uploadFile',
      label: '文件上传',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfUploadFile',
      tagIcon: 'icon-ym icon-ym-generator-upload',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: [],
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    disabled: false,
    accept: '',
    fileSize: 10,
    sizeUnit: 'MB',
    buttonText: '点击上传',
    limit: 9,
    pathType: 'defaultPath',
    sortRule: [],
    timeFormat: 'YYYY',
    folder: '',
    tipText: '',
  },
  {
    __config__: {
      jnpfKey: 'uploadImg',
      label: '图片上传',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfUploadImg',
      tagIcon: 'icon-ym icon-ym-generator-upload',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: [],
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    disabled: false,
    fileSize: 10,
    sizeUnit: 'MB',
    limit: 9,
    pathType: 'defaultPath',
    sortRule: [],
    timeFormat: 'YYYY',
    folder: '',
    tipText: '',
  },
  {
    __config__: {
      jnpfKey: 'colorPicker',
      label: '颜色选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfColorPicker',
      tagIcon: 'icon-ym icon-ym-generator-color',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    showAlpha: false,
    colorFormat: '',
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'iconPicker',
      label: '图标选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfIconPicker',
      tagIcon: 'ym-custom ym-custom-flag-outline',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'rate',
      label: '评分',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfRate',
      tagIcon: 'icon-ym icon-ym-generator-rate',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: 0,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    count: 5,
    allowHalf: false,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'slider',
      label: '滑块',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfSlider',
      tagIcon: 'icon-ym icon-ym-generator-slider',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: 0,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    disabled: false,
    min: 0,
    max: 100,
    step: 1,
  },
  {
    __config__: {
      jnpfKey: 'editor',
      label: '富文本',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfEditor',
      tagIcon: 'icon-ym icon-ym-generator-rich-text',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'blur',
    },
    style: { width: '100%' },
    placeholder: '请输入',
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'link',
      label: '链接',
      labelWidth: undefined,
      showLabel: false,
      tag: 'JnpfLink',
      tagIcon: 'icon-ym icon-ym-generator-link',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    on: {
      click: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    content: '文本链接',
    href: '',
    target: '_self',
    textStyle: {
      'text-align': 'left',
    },
  },
  {
    __config__: {
      jnpfKey: 'button',
      label: '按钮',
      labelWidth: undefined,
      showLabel: false,
      tag: 'JnpfButton',
      tagIcon: 'icon-ym icon-ym-generator-button',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      regList: [],
      trigger: 'click',
    },
    on: {
      click: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    align: 'left',
    buttonText: '按钮',
    type: '',
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'text',
      label: '文本',
      labelWidth: undefined,
      showLabel: false,
      tag: 'JnpfText',
      tagIcon: 'icon-ym icon-ym-generator-textarea',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: undefined,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    textStyle: {
      color: '#000000',
      'text-align': 'left',
      'font-weight': 'normal',
      'font-style': 'normal',
      'text-decoration': 'none',
      'line-height': 32,
      'font-size': 12,
    },
    content: '这是一段文字',
  },
  {
    __config__: {
      jnpfKey: 'alert',
      label: '提示',
      labelWidth: undefined,
      showLabel: false,
      tag: 'JnpfAlert',
      tagIcon: 'icon-ym icon-ym-generator-alert',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    title: '这是一个提示',
    type: 'success',
    showIcon: false,
    closable: true,
    description: '',
    closeText: '',
  },
  {
    __config__: {
      jnpfKey: 'qrcode',
      label: '二维码',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfQrcode',
      tagIcon: 'icon-ym icon-ym-generator-qrcode',
      className: [],
      defaultValue: '',
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
    },
    colorDark: '#000',
    colorLight: '#fff',
    width: 100,
    dataType: 'static',
    staticText: '二维码',
    relationField: '',
  },
  {
    __config__: {
      jnpfKey: 'barcode',
      label: '条形码',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfBarcode',
      tagIcon: 'icon-ym icon-ym-generator-barcode',
      className: [],
      defaultValue: '',
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
    },
    format: 'code128',
    lineColor: '#000',
    background: '#fff',
    width: 4,
    height: 40,
    dataType: 'static',
    staticText: '10241024',
    relationField: '',
  },
];

// 高级控件 【左面板】
export const selectComponents: GenItem[] = [
  {
    __config__: {
      jnpfKey: 'organizeSelect',
      label: '组织选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfOrganizeSelect',
      tagIcon: 'icon-ym icon-ym-generator-company',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: [],
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    ableIds: [],
    multiple: false,
    clearable: true,
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'depSelect',
      label: '部门选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfDepSelect',
      tagIcon: 'icon-ym icon-ym-tree-department1',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    ableIds: [],
    multiple: false,
    clearable: true,
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'posSelect',
      label: '岗位选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfPosSelect',
      tagIcon: 'icon-ym icon-ym-generator-jobs',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    ableIds: [],
    multiple: false,
    clearable: true,
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'userSelect',
      label: '用户选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfUserSelect',
      tagIcon: 'icon-ym icon-ym-generator-user',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    ableIds: [],
    ableRelationIds: [],
    relationField: '',
    multiple: false,
    clearable: true,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'roleSelect',
      label: '角色选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfRoleSelect',
      tagIcon: 'icon-ym icon-ym-generator-role',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    ableIds: [],
    multiple: false,
    clearable: true,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'groupSelect',
      label: '分组选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfGroupSelect',
      tagIcon: 'icon-ym icon-ym-generator-group1',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    ableIds: [],
    multiple: false,
    clearable: true,
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'usersSelect',
      label: '用户组件',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfUsersSelect',
      tagIcon: 'icon-ym icon-ym-generator-founder',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    ableIds: [],
    multiple: false,
    clearable: true,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'table',
      label: '设计子表',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: false,
      tagIcon: 'icon-ym icon-ym-generator-table',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      tag: 'JnpfInputTable',
      defaultValue: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      showTitle: true,
      children: [],
      tableName: '',
      complexHeaderList: [],
    },
    disabled: false,
    showSummary: false,
    summaryField: [],
    defaultValue: [],
    columnBtnsList: [
      { value: 'copy', label: '复制', labelI18nCode: 'common.copyText', show: true, btnType: 'primary', btnIcon: 'icon-ym icon-ym-btn-edit' },
      {
        value: 'remove',
        label: '删除',
        labelI18nCode: 'common.delText',
        show: true,
        btnType: 'danger',
        btnIcon: 'icon-ym icon-ym-btn-clearn',
        showConfirm: true,
      },
    ],
    footerBtnsList: [
      { value: 'add', label: '添加', labelI18nCode: 'common.add1Text', show: true, btnType: 'primary', btnIcon: 'icon-ym icon-ym-btn-add' },
      {
        value: 'batchRemove',
        label: '批量删除',
        labelI18nCode: 'common.batchDelText',
        show: true,
        btnType: 'danger',
        btnIcon: 'icon-ym icon-ym-btn-clearn',
        showConfirm: true,
      },
    ],
    layoutType: 'table',
    defaultExpandAll: true,
  },
  {
    __config__: {
      jnpfKey: 'treeSelect',
      label: '下拉树形',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfTreeSelect',
      tagIcon: 'icon-ym icon-ym-generator-tree',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      dataType: 'static',
      dictionaryType: '',
      propsUrl: '',
      propsName: '',
      useCache: true,
      templateJson: [],
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    options: [
      {
        id: '1',
        fullName: '选项1',
        children: [
          {
            id: '2',
            fullName: '选项1-1',
          },
        ],
      },
    ],
    props: {
      value: 'id',
      label: 'fullName',
      children: 'children',
    },
    placeholder: '请选择',
    multiple: false,
    clearable: true,
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'popupTableSelect',
      label: '下拉表格',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      required: false,
      tag: 'JnpfPopupTableSelect',
      tagIcon: 'icon-ym icon-ym-generator-popupTableSelect',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      useCache: true,
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    interfaceId: '',
    interfaceName: '',
    templateJson: [],
    hasPage: false,
    pageSize: 20,
    extraOptions: [],
    columnOptions: [],
    propsValue: 'id',
    relationField: 'fullName',
    popupType: 'popover',
    popupTitle: '选择数据',
    popupWidth: '800px',
    disabled: false,
    clearable: true,
    multiple: false,
    filterable: true,
  },
  {
    __config__: {
      jnpfKey: 'autoComplete',
      label: '下拉补全',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      required: false,
      tag: 'JnpfAutoComplete',
      tagIcon: 'icon-ym icon-ym-generator-autoComplete',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请输入',
    interfaceId: '',
    interfaceName: '',
    templateJson: [],
    total: 10,
    relationField: 'fullName',
    disabled: false,
    clearable: true,
  },
  {
    __config__: {
      jnpfKey: 'areaSelect',
      label: '省市区域',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfAreaSelect',
      tagIcon: 'icon-ym icon-ym-generator-Provinces',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: [],
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    disabled: false,
    clearable: true,
    filterable: false,
    multiple: false,
    level: 2,
  },
  {
    __config__: {
      jnpfKey: 'relationForm',
      label: '关联表单',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfRelationForm',
      tagIcon: 'icon-ym icon-ym-generator-menu',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      transferList: [],
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    modelId: '',
    relationField: '',
    hasPage: false,
    pageSize: 20,
    queryType: 0,
    propsValue: undefined,
    extraOptions: [],
    columnOptions: [],
    clearable: true,
    popupType: 'dialog',
    popupTitle: '选择数据',
    popupWidth: '800px',
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'relationFormAttr',
      label: '关联表单属性',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfRelationFormAttr',
      tagIcon: 'icon-ym icon-ym-generator-nature',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    style: { width: '100%' },
    showField: '',
    relationField: '',
    isStorage: 0,
  },
  {
    __config__: {
      jnpfKey: 'popupSelect',
      label: '弹窗选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      required: false,
      tag: 'JnpfPopupSelect',
      tagIcon: 'icon-ym icon-ym-generator-popup',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      transferList: [],
      useCache: true,
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    style: { width: '100%' },
    placeholder: '请选择',
    interfaceId: '',
    interfaceName: '',
    templateJson: [],
    hasPage: false,
    pageSize: 20,
    extraOptions: [],
    columnOptions: [],
    propsValue: 'id',
    relationField: 'fullName',
    popupType: 'dialog',
    popupTitle: '选择数据',
    popupWidth: '800px',
    disabled: false,
    clearable: true,
  },
  {
    __config__: {
      jnpfKey: 'popupAttr',
      label: '弹窗选择属性',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfPopupAttr',
      tagIcon: 'icon-ym icon-ym-generator-popup-attr',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    style: { width: '100%' },
    showField: '',
    relationField: '',
    isStorage: 0,
  },
  {
    __config__: {
      jnpfKey: 'signature',
      label: '电子签章',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfSignature',
      tagIcon: 'icon-ym icon-ym-signature1',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    ableIds: [],
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'sign',
      label: '手写签名',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfSign',
      tagIcon: 'icon-ym icon-ym-signature',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    isInvoke: true,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'location',
      label: '定位',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfLocation',
      tagIcon: 'icon-ym icon-ym-generator-location',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    autoLocation: false,
    enableLocationScope: false,
    adjustmentScope: 500,
    enableDesktopLocation: false,
    locationScope: [],
    clearable: true,
    disabled: false,
  },
  {
    __config__: {
      jnpfKey: 'iframe',
      label: 'Iframe',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfIframe',
      tagIcon: 'icon-ym icon-ym-generator-iframe',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc'],
      noShow: false,
    },
    href: '',
    height: 300,
    borderType: 'solid',
    borderColor: '#E2E0E0',
    borderWidth: 1,
  },
  {
    __config__: {
      jnpfKey: 'calculate',
      label: '计算公式',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      required: false,
      tag: 'JnpfCalculate',
      tagIcon: 'icon-ym icon-ym-generator-count',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
    },
    style: { width: '100%' },
    expression: [],
    isStorage: 0,
    thousands: false,
    isAmountChinese: false,
    precision: 2,
    roundType: 1,
  },
];

// 系统控件 【左面板】
export const systemComponents: GenItem[] = [
  {
    __config__: {
      jnpfKey: 'createUser',
      label: '创建人员',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfOpenData',
      tagIcon: 'icon-ym icon-ym-generator-founder',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    type: 'currUser',
    readonly: true,
    placeholder: '',
  },
  {
    __config__: {
      jnpfKey: 'createTime',
      label: '创建时间',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfOpenData',
      tagIcon: 'icon-ym icon-ym-generator-createtime',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      layout: 'colFormItem',
      required: false,
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    type: 'currTime',
    readonly: true,
    placeholder: '',
  },
  {
    __config__: {
      jnpfKey: 'modifyUser',
      label: '修改人员',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfInput',
      tagIcon: 'icon-ym icon-ym-generator-modifier',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    readonly: true,
    placeholder: '系统自动生成',
  },
  {
    __config__: {
      jnpfKey: 'modifyTime',
      label: '修改时间',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfInput',
      tagIcon: 'icon-ym icon-ym-generator-modifytime',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    readonly: true,
    placeholder: '系统自动生成',
  },
  {
    __config__: {
      jnpfKey: 'currOrganize',
      label: '所属组织',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfOpenData',
      tagIcon: 'icon-ym icon-ym-generator-company',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    type: 'currOrganize',
    readonly: true,
    showLevel: 'last',
    placeholder: '',
  },
  {
    __config__: {
      jnpfKey: 'currPosition',
      label: '所属岗位',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfOpenData',
      tagIcon: 'icon-ym icon-ym-generator-station',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    type: 'currPosition',
    readonly: true,
    placeholder: '',
  },
  {
    __config__: {
      jnpfKey: 'billRule',
      label: '单据组件',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'JnpfInput',
      tagIcon: 'icon-ym icon-ym-generator-documents',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      layout: 'colFormItem',
      required: false,
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      trigger: 'change',
      rule: '',
      ruleName: '',
      ruleType: 1,
      ruleConfig: {},
    },
    style: { width: '100%' },
    readonly: true,
    placeholder: '系统自动生成',
  },
  {
    __config__: {
      jnpfKey: 'dataLogList',
      label: '修改记录',
      labelWidth: undefined,
      showLabel: false,
      tag: 'DataLogList',
      tagIcon: 'ym-custom ym-custom-history',
      tableAlign: 'left',
      tableFixed: 'none',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    readonly: true,
    placeholder: '',
    modelId: '',
  },
];

// 布局控件 【左面板】
export const layoutComponents: GenItem[] = [
  {
    __config__: {
      jnpfKey: 'groupTitle',
      label: '分组标题',
      labelWidth: undefined,
      showLabel: false,
      tag: 'JnpfGroupTitle',
      tagIcon: 'icon-ym icon-ym-generator-group',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    helpMessage: '',
    content: '分组标题',
    contentPosition: 'left',
  },
  {
    __config__: {
      jnpfKey: 'divider',
      label: '分割线',
      labelWidth: undefined,
      showLabel: false,
      tag: 'JnpfDivider',
      tagIcon: 'icon-ym icon-ym-generator-divider',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    content: '我是分割线',
    contentPosition: 'center',
  },
  {
    __config__: {
      jnpfKey: 'collapse',
      label: '折叠面板',
      labelWidth: undefined,
      showLabel: false,
      tag: 'ACollapse',
      tagIcon: 'icon-ym icon-ym-generator-fold',
      className: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      children: [
        {
          title: '面板1',
          name: '1',
          __config__: {
            jnpfKey: 'collapseItem',
            children: [],
          },
        },
        {
          title: '面板2',
          name: '2',
          __config__: {
            jnpfKey: 'collapseItem',
            children: [],
          },
        },
      ],
      active: ['1'],
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    accordion: false,
    ghost: true,
    expandIconPosition: 'end',
  },
  {
    __config__: {
      jnpfKey: 'tab',
      label: '标签面板',
      labelWidth: undefined,
      showLabel: false,
      tag: 'ATab',
      tagIcon: 'icon-ym icon-ym-generator-label',
      className: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      children: [
        {
          title: 'Tab 1',
          name: '1',
          __config__: {
            jnpfKey: 'tabItem',
            children: [],
          },
        },
        {
          title: 'Tab 2',
          name: '2',
          __config__: {
            jnpfKey: 'tabItem',
            children: [],
          },
        },
      ],
      active: '1',
    },
    on: {
      tabClick: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    type: '',
    tabPosition: 'top',
  },
  {
    __config__: {
      jnpfKey: 'steps',
      label: '步骤条',
      labelWidth: undefined,
      showLabel: false,
      tag: 'ASteps',
      tagIcon: 'icon-ym icon-ym-generator-steps',
      className: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      children: [
        {
          title: '步骤1',
          name: '1',
          icon: '',
          __config__: {
            jnpfKey: 'stepItem',
            children: [],
          },
        },
        {
          title: '步骤2',
          name: '2',
          icon: '',
          __config__: {
            jnpfKey: 'stepItem',
            children: [],
          },
        },
      ],
      active: 0,
    },
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    simple: false,
    processStatus: 'process',
  },
  {
    __config__: {
      jnpfKey: 'row',
      label: '栅格容器',
      labelWidth: undefined,
      showLabel: false,
      tag: 'ARow',
      tagIcon: 'icon-ym icon-ym-generator-layout',
      className: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    type: 'default',
    justify: 'start',
    align: 'top',
  },
  {
    __config__: {
      jnpfKey: 'card',
      label: '卡片容器',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: false,
      tag: 'ACard',
      tagIcon: 'icon-ym icon-ym-generator-card',
      className: [],
      defaultValue: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      children: [],
    },
    header: '卡片容器',
    shadow: 'never',
  },
  {
    __config__: {
      jnpfKey: 'tableGrid',
      label: '表格容器',
      labelWidth: undefined,
      showLabel: false,
      tag: 'Table',
      tagIcon: 'icon-ym icon-ym-generator-tableGrid',
      className: [],
      defaultValue: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      borderType: 'solid',
      borderColor: '#E2E0E0',
      borderWidth: 1,
      children: [
        {
          __config__: {
            jnpfKey: 'tableGridTr',
            children: [
              {
                __config__: {
                  jnpfKey: 'tableGridTd',
                  merged: false,
                  colspan: 1,
                  rowspan: 1,
                  children: [],
                  backgroundColor: '',
                },
              },
            ],
          },
        },
      ],
    },
  },
];
