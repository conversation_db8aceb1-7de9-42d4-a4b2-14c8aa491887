<template>
  <div ref="chartRef" style="width: 100%; height: 300px"></div>
</template>
<script lang="ts" setup>
import { useEChart } from "@/components/VisualPortal/Design/hooks/useEChart";
import { onMounted, ref, Ref, watch } from "vue";

const props = defineProps(["grouped"]);

let activeData = {
  jnpfKey: "pieChart",
  dataType: "static",
  option: {
    styleType: 2,
    defaultValue: [],
    roseType: false,
    roseType1: false,
    titleText: "",
    titleTextStyleColor: "#303133",
    titleTextStyleFontSize: 18,
    titleTextStyleFontWeight: false,
    titleLeft: "center",
    titleBgColor: "",
    titleSubtext: "",
    titleSubtextStyleColor: "#303133",
    titleSubtextStyleFontSize: null,
    titleSubtextStyleFontWeight: false,
    seriesLabelShow: false,
    seriesLabelPosition: "outside",
    seriesLabelShowInfo: ["count", "percent"],
    seriesCenterLeft: 50,
    seriesCenterTop: 39,
    seriesLabelFontSize: 14,
    seriesLabelFontWeight: false,
    seriesLabelColor: "#303133",
    seriesLabelBgColor: "",
    seriesLineStyleWidth: 20,
    seriesSymbolRotate: 4,
    tooltipShow: true,
    tooltipTextStyleFontSize: 14,
    tooltipTextStyleFontWeight: false,
    tooltipTextStyleColor: "#303133",
    tooltipBgColor: "#fff",
    gridBottom: 30,
    gridLeft: 20,
    gridRight: 20,
    gridTop: 10,
    legendShow: true,
    legendTextStyleFontSize: 14,
    legendOrient: "horizontal",
    legendLeft: 0,
    legendTop: null,
    legendBottom: 10,
    AxisTextStyleColor: "",
    AxisLineStyleColor: "",
    colorList: [],
    target: "_self",
    urlAddress: "",
    sortable: true,
  },
  refresh: {
    autoRefresh: false,
    autoRefreshTime: 5,
  },
};

const chartRef = ref<HTMLDivElement | null>(null);
const { CardHeader, init } = useEChart(activeData, chartRef as Ref<HTMLDivElement>);
// onMounted(() => init());
watch(
  async () => props.grouped,
  (newVal, oldVal) => {
    if (newVal) {
      activeData.option.defaultValue = JSON.parse(JSON.stringify(props.grouped));
      // let current = JSON.parse(JSON.stringify(props.grouped));
      // activeData.option.defaultValue = current.map((item) => {
      //   return {
      //     name: item.f_custom_task_name,
      //     type: "任务进度",
      //     value: item.f_progress,
      //   };
      // });
      init();
    }
  }
);
</script>
