<template>
  <DynamicPortal class="custom-statistic-portal" portalId="717656749070356037"></DynamicPortal>
  <a-tabs class="jnpf-content-wrapper-tabs" v-model:activeKey="activeKey" @change="handleTabChange">
    <a-tab-pane v-for="tab in tabs" :key="tab.id" :tab="tab.label" />
  </a-tabs>

  <div class="jnpf-content-detail-extra" :loading="loading">
    <!-- 任务管理列表 -->
    <template v-if="modelId === tabs[0].id && !loading">
      <List ref="listRef" class="sublist" :config="state.tabConfig[modelId]" :modelId="modelId" :menuId="menuId">
        <template #column-f_task_code="{ record, column }">
          <p class="link-text" @click="handleTaskCodeClick(record)">{{ record[column.prop] }}</p>
        </template>
        <template #column-f_task_status="{ record, column }">
          <PzTag size="small" :color="getTaskStatusColor(record.f_task_status_jnpfId)" :border="false">{{ record[column.prop] }}</PzTag>
        </template>
      </List>
    </template>
    <!-- 成果和档案管理 -->
    <template v-else-if="modelId && !loading">
      <List :key="modelId" class="sublist" :config="state.tabConfig[modelId]" :modelId="modelId" :menuId="menuId"></List>
    </template>
  </div>
  <TaskDetail ref="detailRef" />
  <TaskForm ref="formRef" class="pz-style" :reload="reload"/>
</template>

<script lang="ts" setup>
import { reactive, onMounted, toRefs, ref } from 'vue';
  import { getConfigData } from '@/api/onlineDev/visualDev';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useTabs } from '@/hooks/web/useTabs';
  import { getTaskStatusColor } from '@/views/hz/util';
  import List from '@/views/common/dynamicModel/list/index.vue';
  import DynamicPortal from '@/views/common/dynamicPortal/index.vue';
  import TaskDetail from './TaskDetail.vue';
  import TaskForm from './TaskForm.vue';
  import PzTag from '@/components/Pz/PzTag/index.vue';

interface State {
  config: any;
  formConf: any;
  useFormPermission: boolean;
  activeKey: string;
  modelId: string;
  menuId: string;
  tabs: any[];
  loading: boolean;
  tabConfig: any;
}
const state = reactive<State>({
  config: {},
  formConf: {},
  useFormPermission: false,
  activeKey: '705349737007549061',
  modelId: '',
  menuId: '',
  tabs: [
    { label: '任务管理', id: '705349737007549061', menuId: '705350461019916933' },
    { label: '成果管理', id: '717370344746257989', menuId: '717371804213055045' },
  ],
  loading: false,
  tabConfig: {},
});

defineOptions({ name: 'hzTaskIndex' });
const { createMessage } = useMessage();
const { close } = useTabs();
const { activeKey, modelId, menuId, tabs, loading } = toRefs(state);
const listRef: any = ref(null);
const detailRef: any = ref(null);
const formRef: any = ref(null);

// 处理任务代码点击事件
function handleTaskCodeClick(record: any) {
  const data = {
    id: record.id,
    formConf: state.formConf,
    modelId: state.modelId,
    menuId: state.menuId,
    isPreview: false,
    isDataManage: false,
    useFormPermission: state.useFormPermission,
    showMoreBtn: ![3, 5].includes(state.config.columnData.type),
    allList: [],
    record: record,
  };
  formRef.value?.init(data);
}

async function init() {
  return getConfig(state.activeKey);
}

function handleTabChange(key: string) {
  state.activeKey = key;
  getConfig(key);
}

  function getConfig(modelId: string) {
    state.loading = true;
    state.menuId = state.tabs.find(item => item.id === modelId).menuId;
    if (state.tabConfig[modelId]) {
      state.modelId = modelId;
      state.config = state.tabConfig[modelId];
      state.formConf = state.config.formData ? JSON.parse(state.config.formData) : {};
      state.useFormPermission = JSON.parse(state.config.columnData).useFormPermission;
      state.loading = false;
      return;
    }
    getConfigData(modelId).then(res => {
      if (res.code !== 200 || !res.data) {
        close();
        createMessage.error(res.msg || '请求出错，请重试');
        state.loading = false;
        return;
      }
      state.tabConfig[modelId] = res.data;
      state.modelId = modelId;
      state.config = state.tabConfig[modelId];
      state.config.id = state.config.id || modelId;
      state.config.extraQueryJson = JSON.stringify({ f_project_code: localStorage.getItem('project_code') });
      state.formConf = state.config.formData ? JSON.parse(state.config.formData) : {};
      state.useFormPermission = JSON.parse(state.config.columnData).useFormPermission;
      state.loading = false;
    });
  }
  function reload() {
    listRef.value.reload();
  }
  onMounted(() => {
    init();
  });
</script>

<style>
  .custom-statistic-portal {
    height: fit-content !important;
    .scrollbar__wrap {
      margin-bottom: 0 !important;
    }
    .ant-card {
      border: none;
    }
  }
</style>
